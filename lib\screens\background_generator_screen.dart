import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:novel_app/services/background_generator_service.dart';
import 'package:novel_app/controllers/genre_controller.dart';
import 'package:novel_app/controllers/novel_controller.dart';
import 'package:novel_app/widgets/common/loading_overlay.dart';

class BackgroundGeneratorScreen extends StatefulWidget {
  const BackgroundGeneratorScreen({super.key});

  @override
  _BackgroundGeneratorScreenState createState() =>
      _BackgroundGeneratorScreenState();
}

class _BackgroundGeneratorScreenState extends State<BackgroundGeneratorScreen> {
  final TextEditingController _titleController = TextEditingController();
  final TextEditingController _initialIdeaController = TextEditingController();

  final RxString _selectedGenre = ''.obs;
  final RxBool _isDetailed = false.obs;
  final RxBool _isGenerating = false.obs;
  final RxString _generatedBackground = ''.obs;
  final RxString _generationStatus = ''.obs;

  final BackgroundGeneratorService _backgroundGeneratorService =
      Get.find<BackgroundGeneratorService>();
  final GenreController _genreController = Get.find<GenreController>();
  final NovelController _novelController = Get.find<NovelController>();

  @override
  void initState() {
    super.initState();
    // 确保在genres加载完成后设置初始值
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_genreController.genres.isNotEmpty && _selectedGenre.value.isEmpty) {
        _selectedGenre.value = _genreController.genres.first.name;
      }
    });
  }

  @override
  void dispose() {
    _titleController.dispose();
    _initialIdeaController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('故事背景生成器'),
        actions: [
          IconButton(
            icon: const Icon(Icons.save),
            onPressed: _generatedBackground.value.isEmpty
                ? null
                : () {
                    // 保存生成的背景到剪贴板
                    Get.snackbar('成功', '背景已复制到剪贴板');
                  },
          ),
        ],
      ),
      body: Obx(() => LoadingOverlay(
        isLoading: _isGenerating.value,
        loadingText: _generationStatus.value.isEmpty
            ? '正在生成背景...'
            : _generationStatus.value,
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildGenerationForm(),
              const SizedBox(height: 16),
              _buildGeneratedContent(),
            ],
          ),
        ),
      )),
    );
  }

  Widget _buildGenerationForm() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '背景生成设置',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            TextField(
              controller: _titleController,
              decoration: const InputDecoration(
                labelText: '小说标题',
                border: OutlineInputBorder(),
                hintText: '请输入小说标题',
              ),
            ),
            const SizedBox(height: 16),
            Obx(() {
              // 如果genres列表为空，显示加载状态
              if (_genreController.genres.isEmpty) {
                return const DropdownButtonFormField<String>(
                  decoration: InputDecoration(
                    labelText: '小说类型',
                    border: OutlineInputBorder(),
                    hintText: '正在加载类型...',
                  ),
                  items: [],
                  onChanged: null,
                );
              }

              // 确定当前选中的值
              String? currentValue;
              if (_selectedGenre.value.isNotEmpty &&
                  _genreController.genres.any((g) => g.name == _selectedGenre.value)) {
                currentValue = _selectedGenre.value;
              } else if (_genreController.genres.isNotEmpty) {
                currentValue = _genreController.genres.first.name;
                // 同步更新选中值
                WidgetsBinding.instance.addPostFrameCallback((_) {
                  _selectedGenre.value = currentValue!;
                });
              }

              return DropdownButtonFormField<String>(
                decoration: const InputDecoration(
                  labelText: '小说类型',
                  border: OutlineInputBorder(),
                ),
                value: currentValue,
                items: _genreController.genres.map((genre) {
                  return DropdownMenuItem<String>(
                    value: genre.name,
                    child: Text(genre.name),
                  );
                }).toList(),
                onChanged: (value) {
                  if (value != null) {
                    _selectedGenre.value = value;
                  }
                },
              );
            }),
            const SizedBox(height: 16),
            TextField(
              controller: _initialIdeaController,
              decoration: const InputDecoration(
                labelText: '初始构想（可选）',
                border: OutlineInputBorder(),
                hintText: '请输入你的初始构想或关键元素',
              ),
              maxLines: 3,
            ),
            const SizedBox(height: 16),
            Obx(() => SwitchListTile(
                  title: const Text('生成详细背景'),
                  subtitle: const Text('开启后将生成更详细的世界观设定'),
                  value: _isDetailed.value,
                  onChanged: (value) {
                    _isDetailed.value = value;
                  },
                )),
            const SizedBox(height: 16),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _generateBackground,
                child: const Padding(
                  padding: EdgeInsets.symmetric(vertical: 12.0),
                  child: Text('生成背景'),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildGeneratedContent() {
    return Expanded(
      child: Obx(() {
        if (_generatedBackground.value.isEmpty) {
          return const Center(
            child: Text(
              '生成的背景将显示在这里',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey,
              ),
            ),
          );
        }

        return Card(
          elevation: 2,
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    const Text(
                      '生成的背景',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Row(
                      children: [
                        IconButton(
                          icon: const Icon(Icons.copy),
                          onPressed: () => _copyToClipboard(),
                          tooltip: '复制到剪贴板',
                        ),
                        IconButton(
                          icon: const Icon(Icons.add_box),
                          onPressed: () => _showAddToBackgroundDialog(),
                          tooltip: '添加到背景信息',
                        ),
                      ],
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                SelectableText(
                  _generatedBackground.value,
                  style: const TextStyle(
                    fontSize: 16,
                    height: 1.5,
                  ),
                ),
              ],
            ),
          ),
        );
      }),
    );
  }

  Future<void> _generateBackground() async {
    // 验证输入
    if (_titleController.text.isEmpty) {
      Get.snackbar('错误', '请输入小说标题');
      return;
    }

    if (_selectedGenre.value.isEmpty) {
      Get.snackbar('错误', '请选择小说类型');
      return;
    }

    // 开始生成
    _isGenerating.value = true;
    _generationStatus.value = '正在初始化生成参数...';

    try {
      final background = await _backgroundGeneratorService.generateBackground(
        title: _titleController.text,
        genre: _selectedGenre.value,
        initialIdea: _initialIdeaController.text,
        isDetailed: _isDetailed.value,
        onStatusUpdate: (status) {
          _generationStatus.value = status;
        },
      );

      _generatedBackground.value = background;

      // 生成完成后显示选择对话框
      _showGenerationCompleteDialog();

    } catch (e) {
      Get.snackbar('错误', '生成背景失败: $e');
    } finally {
      _isGenerating.value = false;
      _generationStatus.value = '';
    }
  }

  /// 显示生成完成对话框
  void _showGenerationCompleteDialog() {
    Get.dialog(
      AlertDialog(
        title: const Text('背景生成完成'),
        content: const Text('背景已成功生成！您希望如何处理这个背景？'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('仅查看'),
          ),
          FilledButton(
            onPressed: () {
              Get.back();
              _addToNovelBackground();
            },
            child: const Text('添加到背景信息'),
          ),
        ],
      ),
    );
  }

  /// 复制到剪贴板
  void _copyToClipboard() {
    Clipboard.setData(ClipboardData(text: _generatedBackground.value));
    Get.snackbar('成功', '背景已复制到剪贴板');
  }

  /// 显示添加到背景信息的对话框
  void _showAddToBackgroundDialog() {
    Get.dialog(
      AlertDialog(
        title: const Text('添加到背景信息'),
        content: const Text('确定要将此背景添加到当前小说的背景信息中吗？'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('取消'),
          ),
          FilledButton(
            onPressed: () {
              Get.back();
              _addToNovelBackground();
            },
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }

  /// 添加到小说背景信息
  void _addToNovelBackground() {
    if (_generatedBackground.value.isEmpty) {
      Get.snackbar('错误', '没有可添加的背景内容');
      return;
    }

    // 获取当前背景信息
    String currentBackground = _novelController.background.value;

    // 如果当前背景为空，直接设置
    if (currentBackground.isEmpty) {
      _novelController.updateBackground(_generatedBackground.value);
    } else {
      // 如果已有背景，询问是否替换或追加
      Get.dialog(
        AlertDialog(
          title: const Text('背景信息处理'),
          content: const Text('检测到已有背景信息，您希望如何处理？'),
          actions: [
            TextButton(
              onPressed: () => Get.back(),
              child: const Text('取消'),
            ),
            TextButton(
              onPressed: () {
                Get.back();
                // 追加到现有背景
                String newBackground = '$currentBackground\n\n--- 新增背景 ---\n\n${_generatedBackground.value}';
                _novelController.updateBackground(newBackground);
                Get.snackbar('成功', '背景已追加到现有背景信息中');
              },
              child: const Text('追加'),
            ),
            FilledButton(
              onPressed: () {
                Get.back();
                // 替换现有背景
                _novelController.updateBackground(_generatedBackground.value);
                Get.snackbar('成功', '背景已替换现有背景信息');
              },
              child: const Text('替换'),
            ),
          ],
        ),
      );
      return;
    }

    Get.snackbar('成功', '背景已添加到小说背景信息中');
  }
}
